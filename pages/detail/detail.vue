<template>
  <view class="container">
    <web-view :src="url"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      url: '',
      title: ''
    }
  },
  onLoad(options) {
    this.url = decodeURIComponent(options.url || '')
    this.title = decodeURIComponent(options.title || '')
    uni.setNavigationBarTitle({ title: this.title || '新闻详情' })
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100%;
}
</style>