<template>
  <view class="container">
    <view v-for="item in newsList" :key="item.id" class="news-item" @click="goToDetail(item)">
      <text class="title">{{ item.title }}</text>
      <text class="source">{{ item.source.domain }}</text>
      <text class="date">{{ formatDate(item.published_at) }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      newsList: []
    }
  },
  onLoad() {
    this.fetchNews()
  },
  methods: {
    async fetchNews() {
      const API_KEY = 'YOUR_API_KEY'
      const url = `https://cryptopanic.com/api/v1/posts/?auth_token=${API_KEY}&public=true`

      uni.request({
        url,
        success: (res) => {
          this.newsList = res.data.results || []
        },
        fail: (err) => {
          console.error('Failed to load news:', err)
        }
      })
    },
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/detail/detail?url=${encodeURIComponent(item.url)}&title=${encodeURIComponent(item.title)}`
      })
    },
    formatDate(dateStr) {
      return new Date(dateStr).toLocaleString()
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
}
.news-item {
  border-bottom: 1px solid #eee;
  padding: 20rpx 0;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
}
.source {
  font-size: 24rpx;
  color: #888;
}
.date {
  font-size: 24rpx;
  color: #bbb;
}
</style>